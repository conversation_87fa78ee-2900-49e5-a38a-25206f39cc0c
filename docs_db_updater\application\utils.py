import os
import re
import zipfile
import shutil
import hashlib
import logging
import requests
import base64
import time
from langchain.text_splitter import Markdown<PERSON>eaderTextSplitter
import markdownify
from bs4 import BeautifulSoup
from docs_db_updater.application import constants as const

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=const.headers_to_split_on)

def text_to_anchor(text):
    """
    Convert text to an HTML anchor format.
    """
    anchor = text.lower()
    anchor = anchor.replace(" ", "-")
    anchor = re.sub("[^0-9a-zA-Z-]", "", anchor)
    anchor = "#" + anchor
    return anchor

def create_formatted_chunk(chunk, file_name, doc_link, embed):
    """
    Create a formatted chunk with metadata for vector storage.
    """
    formatted_chunk = {const.METADATA: {}}
    formatted_chunk[const.METADATA][const.FILE_NAME] = file_name
    formatted_chunk[const.METADATA][const.DOC_LINK] = doc_link
    if const.HEADER3 in chunk.metadata.keys():
        formatted_chunk[const.METADATA][const.HEADER3] = chunk.metadata[const.HEADER3]
    if const.HEADER2 in chunk.metadata.keys():
        formatted_chunk[const.METADATA][const.HEADER2] = chunk.metadata[const.HEADER2]
    if const.HEADER1 in chunk.metadata.keys():
        formatted_chunk[const.METADATA][const.HEADER1] = chunk.metadata[const.HEADER1]
    formatted_chunk[const.TEXT] = chunk.page_content
    formatted_chunk[const.VECTOR] = embed.embed_query(chunk.page_content)
    return formatted_chunk

def chunk_docs(file_name, file_content, embed, update=False):
    """
    Split markdown content into chunks and process them.

    Args:
        file_name: Name of the file being processed
        file_content: Content of the file to chunk
        embed: Embedding model to use for vectorization
        update: Whether to update the chunk with formatted metadata

    Returns:
        list: List of document chunks
    """
    data = []
    chunked_doc = markdown_splitter.split_text(file_content)
    for chunk in chunked_doc:
        suffix = ""
        if const.HEADER3 in chunk.metadata.keys():
            suffix = text_to_anchor(chunk.metadata[const.HEADER3])
        elif const.HEADER2 in chunk.metadata.keys():
            suffix = text_to_anchor(chunk.metadata[const.HEADER2])

        # Get web path and doc path from environment variables with fallback to constants
        web_path = os.environ.get(const.WEB_PATH, const.WEB_PATH)
        doc_path = os.environ.get(const.DOC_PATH, const.DOC_PATH)

        doc_link = web_path + file_name[len(doc_path):-3] + "/" + suffix
        chunk.metadata[const.FILE_NAME] = file_name
        chunk.metadata[const.DOC_LINK] = doc_link

        # Replace relative paths with absolute URLs
        chunk.page_content = chunk.page_content.replace("../../", f"{web_path}")
        chunk.page_content = chunk.page_content.replace("../", f"{web_path}")
        chunk.page_content = chunk.page_content.replace(".md", "")
        chunk.page_content = chunk.page_content.replace("{.cInlineImage-full}", "")

        # Format the content with headers
        header1_text = '#' + chunk.metadata[const.HEADER1] if const.HEADER1 in chunk.metadata.keys() else ''
        header2_text = '\n##' + chunk.metadata[const.HEADER2] if const.HEADER2 in chunk.metadata.keys() else ''
        header3_text = '\n###' + chunk.metadata[const.HEADER3] if const.HEADER3 in chunk.metadata.keys() else ''
        content_text = '\n' + chunk.page_content
        chunk.page_content = f"{header1_text}{header2_text}{header3_text}{content_text}"

        if update:
            chunk = create_formatted_chunk(chunk, file_name, doc_link, embed)
        data.append(chunk)
    return data

def delete_records(filename, milvus_client):
    """
    Delete records associated with a specific filename from the collection.
    """
    primary_keys = []
    filtered_records = milvus_client.query(collection_name=os.environ.get(const.DOCS_COLLECTION),
                                           filter=f"{const.METADATA}['{const.FILE_NAME}'] == '{filename}'",
                                           output_fields=["pk"])
    for filtered_record in filtered_records:
        primary_keys.append(filtered_record["pk"])
    milvus_client.delete(collection_name=os.environ.get(const.DOCS_COLLECTION), filter=f"pk in {primary_keys}")
    return f"Successfully deleted {len(filtered_records)} records of {filename}"

def add_records(filename, file_content, milvus_client, embed):
    """
    Add records from file content to the collection.
    """
    chunked_docs = chunk_docs(filename, file_content, embed, update=True)
    milvus_client.insert(collection_name=os.environ.get(const.DOCS_COLLECTION), data=chunked_docs)
    return f"Successfully added {len(chunked_docs)} records from {filename}"

def process_changes(added, modified, deleted, milvus_client, embed):
    """
    Process changes (added, modified, deleted files) in the collection.
    """
    for filename, content in added:
        msg = add_records(filename, content, milvus_client, embed)
        logger.info(msg)
    for filename, content in modified:
        msg = delete_records(filename, milvus_client)
        logger.info(msg)
        msg = add_records(filename, content, milvus_client, embed)
        logger.info(msg)
    for filename in deleted:
        msg = delete_records(filename, milvus_client)
        logger.info(msg)

    logger.info(f"File operation summary: {len(added)} added, {len(modified)} modified, {len(deleted)} deleted")

def get_latest_release_data():
    """
    Get the latest release tag and assets from the repository.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/releases/latest"
    headers = {
        const.AUTHORIZATION: f'token {os.environ.get("GITHUB_TOKEN")}',
        const.ACCEPT: "application/vnd.github.v3+json"
    }
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()

    release_data = response.json()
    return release_data["tag_name"], release_data.get("assets", [])

def download_and_extract(url, extract_path, max_extraction_time=300):
    """
    Download and extract a zip file with timeout protection.

    Args:
        url: The URL to download from
        extract_path: The path to extract files to
        max_extraction_time: Maximum time in seconds allowed for extraction
    """
    headers = {
        const.AUTHORIZATION: f'token {os.environ.get("GITHUB_TOKEN")}',
        const.ACCEPT: "application/octet-stream"
    }

    # Use the mounted volume for temporary files
    temp_path = os.path.join("/tmp", f"download_{int(time.time())}.zip")

    try:
        # Check disk space before downloading
        if not check_disk_space("/tmp", 300):  # Need at least 300MB
            logger.error("Not enough disk space for download")
            return

        logger.info(f"Downloading {url} to {temp_path}")

        # Download with progress reporting
        response = requests.get(url, headers=headers, stream=True, timeout=const.TIMEOUT)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        last_log_time = time.time()

        with open(temp_path, "wb") as temp_file:
            for chunk in response.iter_content(chunk_size=1024*1024):  # 1MB chunks
                if chunk:
                    temp_file.write(chunk)
                    downloaded_size += len(chunk)

                    # Log progress every 5 seconds
                    current_time = time.time()
                    if current_time - last_log_time > 5:
                        if total_size > 0:
                            percent = (downloaded_size / total_size) * 100
                            logger.info(f"Downloaded {downloaded_size/(1024*1024):.1f}MB of {total_size/(1024*1024):.1f}MB ({percent:.1f}%)")
                        else:
                            logger.info(f"Downloaded {downloaded_size/(1024*1024):.1f}MB")
                        last_log_time = current_time

        # Get file size for logging
        file_size = os.path.getsize(temp_path)
        logger.info(f"Download complete, file size: {file_size} bytes")

        if file_size == 0:
            logger.error("Downloaded file is empty")
            return

        # Verify zip file integrity
        try:
            logger.info("Verifying zip file integrity...")
            with zipfile.ZipFile(temp_path, "r") as zip_ref:
                # Just list the files to verify the zip is valid
                file_list = zip_ref.namelist()
                logger.info(f"Zip file contains {len(file_list)} entries")

                # Log the first few entries for debugging
                if file_list:
                    sample = file_list[:5] if len(file_list) > 5 else file_list
                    logger.info(f"Sample entries: {sample}")
        except zipfile.BadZipFile as e:
            logger.error(f"Invalid zip file: {e}")
            return

        # Extract with detailed logging and timeout protection
        logger.info(f"Starting extraction to {extract_path}")
        extraction_start_time = time.time()

        try:
            with zipfile.ZipFile(temp_path, "r") as zip_ref:
                total_files = len(zip_ref.namelist())
                # Extract files in batches to avoid timeouts
                batch_size = 500  # Process 500 files at a time

                for batch_start in range(0, total_files, batch_size):
                    # Check if we've exceeded the maximum extraction time
                    if time.time() - extraction_start_time > max_extraction_time:
                        logger.error(f"Extraction timeout after {max_extraction_time} seconds")
                        break

                    batch_end = min(batch_start + batch_size, total_files)
                    logger.info(f"Extracting batch {batch_start//batch_size + 1}/{(total_files + batch_size - 1)//batch_size}")

                    for i in range(batch_start, batch_end):
                        file = zip_ref.namelist()[i]
                        if i % 100 == 0:  # Log progress every 100 files
                            logger.info(f"Extracting file {i+1}/{total_files}")
                            # Also log elapsed time
                            elapsed = time.time() - extraction_start_time
                            logger.info(f"Extraction time: {elapsed:.1f} seconds")

                        try:
                            zip_ref.extract(file, extract_path)
                        except Exception as e:
                            logger.error(f"Failed to extract file {file}: {e}")

                    # Check disk space after each batch
                    if not check_disk_space("/tmp", 100):  # Need at least 100MB
                        logger.error("Running out of disk space during extraction")
                        break

                logger.info(f"Extraction completed or timed out after {time.time() - extraction_start_time:.1f} seconds")
        except Exception as e:
            logger.error(f"Error during extraction: {e}", exc_info=True)
            return

        logger.info(f"Extracted release to {extract_path}")

        # Check if extraction was successful
        try:
            # Count files more efficiently
            file_count = 0
            dir_count = 0
            for _, dirs, files in os.walk(extract_path):
                file_count += len(files)
                dir_count += len(dirs)
                # Break early if we've found some files
                if file_count > 0 and file_count % 1000 == 0:
                    logger.info(f"Counted {file_count} files so far...")

            logger.info(f"Extracted {file_count} files and {dir_count} directories to {extract_path}")

            if file_count == 0:
                logger.error(f"No files were extracted to {extract_path}")
        except Exception as e:
            logger.error(f"Error counting extracted files: {e}")
    except Exception as e:
        logger.error(f"Error downloading or extracting {url}: {e}", exc_info=True)
    finally:
        if os.path.exists(temp_path):
            try:
                os.remove(temp_path)
                logger.info(f"Removed temp zip file {temp_path}")
            except Exception as e:
                logger.error(f"Failed to remove temp file {temp_path}: {e}")

def create_formatted_file_path(path):
    """
    Convert a file path to a standardized format.
    """
    file_path = path.replace(os.path.sep, "/")
    file_path = file_path.replace("out-prod/", "en/")
    file_path = file_path.replace("/index.html", ".md")
    return file_path

def get_markdown(html):
    """
    Convert HTML content to markdown format.
    """
    soup = BeautifulSoup(html, "html.parser")
    content_inner = soup.find("article")
    if not content_inner:
        return None # No <article> tag found in the HTML content
    markdown = markdownify.markdownify(str(content_inner), heading_style="ATX")
    return markdown

def get_chunked_docs(asset, embed):
    """
    Download and process documents from a release asset.

    Args:
        asset: Release asset containing document files
        embed: Embedding model to use for vectorization

    Returns:
        list: List of chunked documents
    """
    chunked_docs = []
    logger.info(f"Downloading latest release from: {asset['browser_download_url']}")

    # Use the mounted volume for temporary directory
    temp_dir = os.path.join("/tmp", f"chunked_docs_{int(time.time())}")
    os.makedirs(temp_dir, exist_ok=True)

    try:
        logger.info(f"Created temporary directory: {temp_dir}")
        download_and_extract(asset["browser_download_url"], temp_dir)

        # Check if extraction was successful
        if not os.listdir(temp_dir):
            logger.error(f"No files were extracted to {temp_dir}")
            return chunked_docs

        logger.info("Processing HTML files...")
        file_count = 0
        processed_count = 0

        for root, _, files in os.walk(temp_dir):
            for file in files:
                if file.endswith(".html"):
                    file_count += 1
                    file_path = os.path.join(root, file)

                    rel_path = os.path.relpath(file_path, temp_dir)
                    if rel_path.replace(os.path.sep, "/") in eval(os.environ.get(const.IGNORE_REL_PATHS, ())):
                        continue

                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            html = f.read()
                            markdown = get_markdown(html)
                            if not markdown:
                                continue
                            filename = create_formatted_file_path(rel_path)
                            chunks = chunk_docs(filename, markdown, embed)
                            chunked_docs.extend(chunks)
                            processed_count += 1
                    except Exception as e:
                        logger.error(f"Error processing file {file_path}: {e}")
                        continue

        logger.info(f"Processed {processed_count} out of {file_count} HTML files")
        return chunked_docs
    finally:
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logger.info(f"Removed temporary directory {temp_dir}")
        except Exception as e:
            logger.error(f"Error removing directory {temp_dir}: {e}")

def check_disk_space(path="/tmp", min_free_mb=500):
    """
    Check if there's enough free disk space in the specified path.

    Args:
        path: Directory path to check for free space
        min_free_mb: Minimum required free space in megabytes

    Returns:
        bool: True if there's enough free space, False otherwise
    """
    try:
        stat = os.statvfs(path)
        free_bytes = stat.f_bavail * stat.f_frsize
        free_mb = free_bytes / (1024 * 1024)
        logger.info(f"Free disk space: {free_mb:.2f} MB")
        return free_mb >= min_free_mb
    except Exception as e:
        logger.error(f"Error checking disk space: {e}")
        return True  # Assume there's enough space if we can't check

def hash_file(file_path):
    """
    Generate a SHA-256 hash of a file.

    Args:
        file_path: Path to the file to hash

    Returns:
        str: Hexadecimal digest of the file hash
    """
    try:
        hasher = hashlib.sha256()
        with open(file_path, "rb") as file:
            # Read file in chunks to handle large files
            for chunk in iter(lambda: file.read(4096), b""):
                hasher.update(chunk)
        return hasher.hexdigest()
    except Exception as e:
        logger.error(f"Error hashing file {file_path}: {e}")
        # Return a placeholder hash to avoid breaking the process
        return f"error-hash-{int(time.time())}"

def get_release_assets(tag):
    """
    Get assets from a specific release tag.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/releases/tags/{tag}"
    headers = {const.AUTHORIZATION: f'token {os.environ.get("GITHUB_TOKEN")}',
               const.ACCEPT: "application/vnd.github.v3+json"}
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()

    return response.json().get("assets", [])

def process_single_release(release_tag, extract_path):
    """
    Process a single release and return a dictionary of file paths to hashes.

    Args:
        release_tag: The release tag to process
        extract_path: Path where release files will be extracted

    Returns:
        dict: Dictionary mapping file paths to their hash values
    """
    try:
        # Get assets for the release
        release_assets = get_release_assets(release_tag)
        logger.info(f"Found {len(release_assets)} assets for {release_tag}")

        if not release_assets:
            logger.error(f"No assets found for {release_tag}")
            return {}

        # Get download URL
        release_zip_url = release_assets[0]["browser_download_url"]

        # Download and extract
        logger.info(f"Downloading and extracting {release_tag}...")
        download_and_extract(release_zip_url, extract_path)

        # Check if extraction was successful
        if not os.path.exists(extract_path) or not os.listdir(extract_path):
            logger.error(f"Extraction failed for {release_tag}")
            return {}

        # Build file hash dictionary
        logger.info(f"Building file hash dictionary for {release_tag}...")
        release_files = {}
        file_count = 0
        html_count = 0

        # Process files in batches to reduce memory usage
        for dp, _, filenames in os.walk(extract_path):
            html_files = [f for f in filenames if f.endswith(".html") and
                         f.replace(os.path.sep, "/") not in eval(os.environ.get(const.IGNORE_REL_PATHS, ()))]
            file_count += len(filenames)
            html_count += len(html_files)

            # Log progress
            if html_files:
                logger.info(f"Processing {len(html_files)} HTML files in {dp}")

            for i, f in enumerate(html_files):
                if i % 100 == 0 and i > 0:
                    logger.info(f"Processed {i}/{len(html_files)} files in directory {dp}")

                try:
                    rel_path = os.path.relpath(os.path.join(dp, f), extract_path)
                    release_files[rel_path] = hash_file(os.path.join(dp, f))
                except Exception as e:
                    logger.error(f"Error hashing file {f}: {e}")

        logger.info(f"Processed {html_count} HTML files out of {file_count} total files from {release_tag}")
        return release_files
    except Exception as e:
        logger.error(f"Error processing release {release_tag}: {e}", exc_info=True)
        return {}

def compare_releases(last_updated_release_tag, latest_release_tag):
    """
    Compare two releases and identify added, modified, and deleted files.

    Args:
        last_updated_release_tag: The previous release tag
        latest_release_tag: The current release tag

    Returns:
        tuple: Lists of added, modified, and deleted files
    """
    logger.info(f"Comparing releases: {last_updated_release_tag} -> {latest_release_tag}")

    # Check disk space before starting
    if not check_disk_space("/tmp", 500):  # Require at least 500MB free
        logger.error("Not enough disk space available. Aborting.")
        return [], [], []

    # Use the mounted volume path in Choreo (/tmp)
    base_temp_dir = "/tmp"

    # Process one release at a time to minimize disk usage
    # Start with a timestamp for unique directory names
    timestamp = int(time.time())

    # Process the older release first
    extract_path = os.path.join(base_temp_dir, f"release_{timestamp}")
    logger.info(f"Creating extraction directory: {extract_path}")

    try:
        # Create directory
        os.makedirs(extract_path, exist_ok=True)

        # Check if directory was created successfully
        if not os.path.exists(extract_path):
            logger.error("Failed to create extraction directory")
            return [], [], []

        # Process the older release
        logger.info(f"Processing previous release: {last_updated_release_tag}")
        last_updated_release_files = process_single_release(last_updated_release_tag, extract_path)

        if not last_updated_release_files:
            logger.error(f"Failed to process previous release {last_updated_release_tag}")
            return [], [], []

        # Clean up after processing the first release
        logger.info(f"Cleaning up directory {extract_path}")
        shutil.rmtree(extract_path)

        # Check disk space again before processing the second release
        if not check_disk_space("/tmp", 500):
            logger.error("Not enough disk space available after processing first release. Aborting.")
            return [], [], []

        # Create a new directory for the latest release
        timestamp = int(time.time())  # Get a new timestamp
        extract_path = os.path.join(base_temp_dir, f"release_{timestamp}")
        logger.info(f"Creating extraction directory: {extract_path}")

        os.makedirs(extract_path, exist_ok=True)

        # Process the newer release
        logger.info(f"Processing latest release: {latest_release_tag}")
        latest_release_files = process_single_release(latest_release_tag, extract_path)

        if not latest_release_files:
            logger.error(f"Failed to process latest release {latest_release_tag}")
            return [], [], []

        logger.info(f"Found {len(last_updated_release_files)} files in previous release and {len(latest_release_files)} files in latest release")

        # Process changes
        added, modified, deleted = [], [], []

        # Process added and modified files
        logger.info("Identifying added and modified files...")
        latest_files_list = list(latest_release_files.keys())
        batch_size = 50  # Process files in smaller batches

        for batch_start in range(0, len(latest_files_list), batch_size):
            batch_end = min(batch_start + batch_size, len(latest_files_list))
            logger.info(f"Processing batch {batch_start//batch_size + 1}/{(len(latest_files_list) + batch_size - 1)//batch_size}")

            for f in latest_files_list[batch_start:batch_end]:
                filename = create_formatted_file_path(f)

                try:
                    if f not in last_updated_release_files:
                        # File is added
                        with open(os.path.join(extract_path, f), "r", encoding="utf-8") as file:
                            html = file.read()
                            markdown = get_markdown(html)
                            if not markdown:
                                continue
                            added.append((filename, markdown))
                    elif latest_release_files[f] != last_updated_release_files[f]:
                        # File is modified
                        with open(os.path.join(extract_path, f), "r", encoding="utf-8") as file:
                            html = file.read()
                            markdown = get_markdown(html)
                            if not markdown:
                                continue
                            modified.append((filename, markdown))
                except Exception as e:
                    logger.error(f"Error processing file {f}: {e}")
                    continue

        # Process deleted files
        logger.info("Identifying deleted files...")
        for f in last_updated_release_files:
            if f not in latest_release_files:
                filename = create_formatted_file_path(f)
                deleted.append(filename)

        logger.info(f"Changes identified: {len(added)} added, {len(modified)} modified, {len(deleted)} deleted")
        return added, modified, deleted

    except Exception as e:
        logger.error(f"Error comparing releases: {e}", exc_info=True)
        return [], [], []
    finally:
        # Clean up
        try:
            if os.path.exists(extract_path):
                logger.info(f"Cleaning up directory {extract_path}")
                shutil.rmtree(extract_path)
                logger.info(f"Removed temp extracted zip file {extract_path}")
        except Exception as e:
            logger.error(f"Error removing directory {extract_path}: {e}")

# ==============================
# Repository-based document processing functions
# ==============================

def retrieve_content(filename):
    """
    Retrieve the content of a file from the repository.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/contents/{filename}?ref={os.environ.get(const.BRANCH)}"
    headers = {const.AUTHORIZATION: f'token {os.environ.get(const.GITHUB_TOKEN)}',
               const.ACCEPT: 'application/vnd.github.v3.raw'}
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()
    if 'base64' in response.headers.get('Content-Encoding', ''):
        try:
            return base64.b64decode(response.content).decode('utf-8')
        except UnicodeDecodeError:
            logger.warning(f"UnicodeDecodeError occurred while decoding {response.content}")
            return ""
    return response.text

def load_md_files_from_repo():
    """
    Load all markdown files from the repository.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/git/trees/{os.environ.get(const.BRANCH)}?recursive=1"
    headers = {const.AUTHORIZATION: f'token {os.environ.get(const.GITHUB_TOKEN)}',
               const.ACCEPT: 'application/vnd.github.v3+json'}
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()
    file_names = [item[const.PATH] for item in response.json().get(const.TREE, [])
                  if item[const.PATH].endswith(const.MD_FORMAT) and os.environ.get(const.MAIN_DIR) in item[const.PATH] and
                  not any(ignore_file in item[const.PATH] for ignore_file in eval(os.environ.get(const.IGNORE_FILES, [])))]
    return file_names

def get_chunked_docs_from_repo(filenames, embed):
    """
    Get chunked documents from the repository.
    """
    chunked_docs = []
    for filename in filenames:
        content = retrieve_content(filename)
        chunks = chunk_docs(filename, content, embed)
        chunked_docs.extend(chunks)
    return chunked_docs

def get_latest_commit():
    """
    Get the latest commit SHA from the repository.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/branches/{os.environ.get(const.BRANCH)}"
    headers = {const.AUTHORIZATION: f'token {os.environ.get(const.GITHUB_TOKEN)}',
               const.ACCEPT: 'application/vnd.github.v3+json'}
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()
    return response.json()['commit']['sha']

def compare_commits(base_sha, head_sha):
    """
    Compare two commits and return the files that have changed.
    """
    url = f"https://api.github.com/repos/{os.environ.get(const.REPO_NAME)}/compare/{base_sha}...{head_sha}"
    headers = {const.AUTHORIZATION: f'token {os.environ.get(const.GITHUB_TOKEN)}',
               const.ACCEPT: 'application/vnd.github.v3+json'}
    response = requests.get(url, headers=headers, timeout=const.TIMEOUT)
    response.raise_for_status()
    return list(response.json()['files'])

def get_diff_from_commits(files):
    """
    Get the diff between two commits.
    """
    added = []
    deleted = []
    for file in files:
        if any(ignore_file in file[const.FILE_NAME] for ignore_file in eval(os.environ.get(const.IGNORE_FILES))):
            continue
        if os.environ.get(const.MAIN_DIR) in file[const.FILE_NAME] and file[const.FILE_NAME].endswith('.md'):
            if file['status'] in ['added', 'modified']:
                added.append(file[const.FILE_NAME])
            elif file['status'] == 'removed':
                deleted.append(file[const.FILE_NAME])
    return added, deleted

def process_repo_changes(added, deleted, milvus_client, embed):
    """
    Process changes in the repository.
    """
    for file in added:
        msg = delete_records(file, milvus_client)
        logger.info(msg)
        msg = add_repo_records(file, milvus_client, embed)
        logger.info(msg)
    for file in deleted:
        msg = delete_records(file, milvus_client)
        logger.info(msg)

def add_repo_records(filename, milvus_client, embed):
    """
    Add records from a repository file.
    """
    file_content = retrieve_content(filename)
    chunked_docs = chunk_docs(filename, file_content, embed, update=True)
    milvus_client.insert(collection_name=os.environ.get(const.DOCS_COLLECTION), data=chunked_docs)
    return f"Successfully added {len(chunked_docs)} records from {filename}"
